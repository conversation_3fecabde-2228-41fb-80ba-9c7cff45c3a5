package com.example.squareshooter

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.*

// Fonctions de dessin selon spécifications

// Dessiner les carrés avec design moderne : Carrés arrondis avec gradients et ombres
fun DrawScope.drawSquareModern(square: Square) {
    val topLeft = Offset(
        square.position.x - square.size / 2,
        square.position.y - square.size / 2
    )
    
    // Ombre
    drawRoundRect(
        color = Color.Black.copy(alpha = 0.3f),
        topLeft = topLeft.copy(x = topLeft.x + 2f, y = topLeft.y + 2f),
        size = Size(square.size, square.size),
        cornerRadius = CornerRadius(8f)
    )
    
    // Carré principal avec gradient
    drawRoundRect(
        brush = Brush.linearGradient(
            colors = listOf(
                square.color,
                square.color.copy(alpha = 0.7f)
            ),
            start = topLeft,
            end = Offset(topLeft.x + square.size, topLeft.y + square.size)
        ),
        topLeft = topLeft,
        size = Size(square.size, square.size),
        cornerRadius = CornerRadius(8f)
    )
    
    // Indicateurs visuels pour carrés résistants : Bordure rouge épaisse + nombre affiché
    if (square.hitsRequired > 1) {
        drawRoundRect(
            color = Color.Red,
            topLeft = topLeft,
            size = Size(square.size, square.size),
            cornerRadius = CornerRadius(8f),
            style = Stroke(width = 3f)
        )
        
        // Nombre affiché au centre
        drawCircle(
            color = Color.White,
            radius = 8f,
            center = square.position
        )
        // Note: Le texte nécessiterait TextPainter, simplifié ici avec un cercle
    }
}

// Balles dorées avec effet de lueur
fun DrawScope.drawBulletGolden(bullet: Bullet) {
    // Effet de lueur
    drawCircle(
        brush = Brush.radialGradient(
            colors = listOf(
                Color(0xFFFFD700).copy(alpha = 0.8f),
                Color(0xFFFFD700).copy(alpha = 0.3f),
                Color.Transparent
            ),
            radius = bullet.radius * 2
        ),
        radius = bullet.radius * 2,
        center = bullet.position
    )
    
    // Balle principale dorée
    drawCircle(
        brush = Brush.radialGradient(
            colors = listOf(
                Color(0xFFFFD700),
                Color(0xFFDAA520)
            )
        ),
        radius = bullet.radius,
        center = bullet.position
    )
}

// Dessiner les canons modernes
fun DrawScope.drawCannonModern(cannon: Cannon) {
    val cannonColor = if (cannon.isMain) Color(0xFF3498DB) else Color(0xFF2980B9)
    
    // Ombre du canon
    drawCircle(
        color = Color.Black.copy(alpha = 0.3f),
        radius = cannon.size,
        center = cannon.position.copy(x = cannon.position.x + 2f, y = cannon.position.y + 2f)
    )
    
    // Corps du canon avec gradient
    drawCircle(
        brush = Brush.radialGradient(
            colors = listOf(
                cannonColor,
                cannonColor.copy(alpha = 0.7f)
            )
        ),
        radius = cannon.size,
        center = cannon.position
    )
    
    // Canon principal plus grand
    if (cannon.isMain) {
        drawCircle(
            color = Color.White.copy(alpha = 0.3f),
            radius = cannon.size * 0.7f,
            center = cannon.position
        )
    }
    
    // Direction du canon (ligne de visée)
    val lineEnd = Offset(
        cannon.position.x + cos(cannon.angle) * cannon.size * 1.5f,
        cannon.position.y + sin(cannon.angle) * cannon.size * 1.5f
    )
    
    drawLine(
        color = Color.White,
        start = cannon.position,
        end = lineEnd,
        strokeWidth = 3f
    )
}

// Interface Moderne selon spécifications
@Composable
fun ModernGameUI(
    gameState: GameState,
    onAutoToggle: () -> Unit,
    onPlaceToggle: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(100.dp),
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2C3E50)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp)
        ) {
            // Informations : 🎯 Score, ⚡ Niveau, 💰 Argent
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                InfoCard("🎯", "Score", gameState.score.toString())
                InfoCard("⚡", "Niveau", gameState.level.toString())
                InfoCard("💰", "Argent", "${gameState.money}$")
            }
            
            // Boutons modernes : Arrondis avec gradients et ombres
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Bouton AUTO ON/OFF
                ModernButton(
                    text = if (gameState.isAutoMode) "AUTO ON" else "AUTO OFF",
                    color = if (gameState.isAutoMode) Color(0xFF00C851) else Color(0xFF95A5A6),
                    onClick = onAutoToggle
                )
                
                // Bouton PLACER (Mode placement de lanceur)
                val canPlace = gameState.cannons.size < gameState.getAvailableLaunchers()
                ModernButton(
                    text = "PLACER",
                    color = if (canPlace) Color(0xFF3498DB) else Color(0xFF95A5A6),
                    onClick = onPlaceToggle,
                    enabled = canPlace
                )
                
                // Indicateur de lanceurs disponibles
                Text(
                    text = "Lanceurs: ${gameState.cannons.size}/${gameState.getAvailableLaunchers()}",
                    color = Color.White,
                    fontSize = 12.sp
                )
            }
        }
    }
}

@Composable
fun InfoCard(icon: String, label: String, value: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "$icon $value",
            color = Color.White,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = label,
            color = Color(0xFFBDC3C7),
            fontSize = 10.sp
        )
    }
}

@Composable
fun ModernButton(
    text: String,
    color: Color,
    onClick: () -> Unit,
    enabled: Boolean = true
) {
    Button(
        onClick = onClick,
        enabled = enabled,
        colors = ButtonDefaults.buttonColors(
            containerColor = color,
            disabledContainerColor = Color(0xFF95A5A6)
        ),
        shape = RoundedCornerShape(20.dp),
        modifier = Modifier
            .height(35.dp)
            .width(80.dp)
    ) {
        Text(
            text = text,
            fontSize = 10.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
    }
}

// Écran Game Over selon spécifications
@Composable
fun GameOverScreen(
    finalScore: Int,
    bestScore: Int,
    level: Int,
    isNewRecord: Boolean,
    onReplay: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                // Arrière-plan : Dégradé sombre dramatique
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF2C3E50),
                        Color(0xFF1A1A1A),
                        Color.Black
                    )
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.5f)
            ),
            shape = RoundedCornerShape(20.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // Titre : "💀 GAME OVER" avec lueur rouge
                Text(
                    text = "💀 GAME OVER",
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Red,
                    textAlign = TextAlign.Center,
                    style = androidx.compose.ui.text.TextStyle(
                        shadow = androidx.compose.ui.graphics.Shadow(
                            color = Color.Red,
                            blurRadius = 15f
                        )
                    )
                )
                
                // Record : Animation spéciale "✨ NOUVEAU RECORD! ✨"
                if (isNewRecord) {
                    Text(
                        text = "✨ NOUVEAU RECORD! ✨",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFFFFD700),
                        textAlign = TextAlign.Center,
                        style = androidx.compose.ui.text.TextStyle(
                            shadow = androidx.compose.ui.graphics.Shadow(
                                color = Color(0xFFFFD700),
                                blurRadius = 10f
                            )
                        )
                    )
                }
                
                // Statistiques : Score final, meilleur score, niveau atteint
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "Score Final: $finalScore",
                        fontSize = 18.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Text(
                        text = "Meilleur Score: $bestScore",
                        fontSize = 16.sp,
                        color = Color(0xFFFFD700)
                    )
                    
                    Text(
                        text = "Niveau Atteint: $level",
                        fontSize = 16.sp,
                        color = Color(0xFF3498DB)
                    )
                }
                
                // Bouton : "REJOUER" pour recommencer
                Button(
                    onClick = onReplay,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF00C851)
                    ),
                    shape = RoundedCornerShape(25.dp),
                    modifier = Modifier
                        .fillMaxWidth(0.7f)
                        .height(50.dp)
                ) {
                    Text(
                        text = "REJOUER",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
            }
        }
    }
}
