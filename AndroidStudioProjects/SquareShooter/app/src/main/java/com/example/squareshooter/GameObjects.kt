package com.example.squareshooter

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import kotlin.math.*
import kotlin.random.Random

// Couleurs modernes pour les carrés
val SQUARE_COLORS = listOf(
    Color(0xFFFF6B6B), // Rouge corail
    Color(0xFF4ECDC4), // Turquoise
    Color(0xFF45B7D1), // Bleu
    Color(0xFF96CEB4), // Vert menthe
    Color(0xFFFECA57), // Jaune
    Color(0xFF9B59B6)  // Violet
)

// Classe pour représenter une balle
data class Bullet(
    var position: Offset,
    val velocity: Offset,
    val radius: Float = 8f,
    var isActive: Boolean = true
) {
    fun update() {
        if (isActive) {
            position = Offset(
                position.x + velocity.x,
                position.y + velocity.y
            )
        }
    }
    
    fun isOutOfBounds(screenWidth: Float, screenHeight: Float): Boolean {
        return position.x < -radius || position.x > screenWidth + radius ||
               position.y < -radius || position.y > screenHeight + radius
    }
}

// Classe pour représenter un carré ennemi
data class Square(
    var position: Offset,
    val size: Float = 40f,
    val color: Color = SQUARE_COLORS.random(),
    var velocity: Float = 1f,
    var hitsRequired: Int = 1,
    var currentHits: Int = 0,
    var isActive: Boolean = true
) {
    fun update() {
        if (isActive) {
            position = Offset(position.x, position.y + velocity)
        }
    }
    
    fun hit(): Boolean {
        currentHits++
        return currentHits >= hitsRequired
    }
    
    fun isAtBottom(screenHeight: Float): Boolean {
        return position.y + size/2 >= screenHeight - 100f // Zone rouge
    }
    
    fun getBounds(): Pair<Offset, Offset> {
        val halfSize = size / 2
        return Pair(
            Offset(position.x - halfSize, position.y - halfSize),
            Offset(position.x + halfSize, position.y + halfSize)
        )
    }
}

// Classe pour représenter un canon
data class Cannon(
    val position: Offset,
    val size: Float = 30f,
    var angle: Float = 0f,
    val isMain: Boolean = true
) {
    fun aimAt(target: Offset) {
        val dx = target.x - position.x
        val dy = target.y - position.y
        angle = atan2(dy, dx)
    }
    
    fun createBullet(speed: Float = 15f): Bullet {
        val bulletVelocity = Offset(
            cos(angle) * speed,
            sin(angle) * speed
        )
        return Bullet(
            position = position.copy(),
            velocity = bulletVelocity
        )
    }
}

// Classe pour gérer l'état du jeu
data class GameState(
    var score: Int = 0,
    var level: Int = 1,
    var money: Int = 0,
    var bestScore: Int = 0,
    var isGameOver: Boolean = false,
    var isAutoMode: Boolean = false,
    var isPlacingLauncher: Boolean = false,
    var bullets: MutableList<Bullet> = mutableListOf(),
    var squares: MutableList<Square> = mutableListOf(),
    var cannons: MutableList<Cannon> = mutableListOf(),
    var frameCount: Int = 0,
    var lastSquareSpawn: Int = 0,
    var lastAutoShot: Int = 0,
    var isNewRecord: Boolean = false
) {
    
    fun getSquareSpeed(): Float {
        return 0.5f + (level - 1) * 0.3f
    }
    
    fun getSquareHits(): Int {
        return when {
            score < 500 -> 1
            score < 1000 -> Random.nextInt(1, 4)
            score < 1500 -> Random.nextInt(2, 5)
            else -> Random.nextInt(2, 6)
        }
    }
    
    fun shouldSpawnSquare(): Boolean {
        val spawnRate = max(30, 120 - level * 5) // Plus rapide avec le niveau
        return frameCount - lastSquareSpawn >= spawnRate
    }
    
    fun canAutoShoot(): Boolean {
        return isAutoMode && frameCount - lastAutoShot >= 20
    }
    
    fun getAvailableLaunchers(): Int {
        return score / 500 + 1
    }
    
    fun addScore(points: Int, isHighZone: Boolean = false) {
        val basePoints = if (isHighZone) points else points + 5
        score += basePoints
        
        // Argent bonus pour zone basse
        if (!isHighZone) {
            money += 2
        }
    }
    
    fun addResistanceBonus(hits: Int) {
        score += hits * 5
        money += 1
    }
    
    fun updateLevel() {
        val newLevel = (score / 300) + 1
        if (newLevel > level) {
            level = newLevel
        }
    }

    fun checkNewRecord(currentBest: Int): Boolean {
        if (score > currentBest) {
            bestScore = score
            isNewRecord = true
            return true
        }
        return false
    }
}
