package com.example.squareshooter.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Couleurs du jeu Square Shooter
val GameBackground = Color(0xFF1A2332)
val GameBackgroundEnd = Color(0xFF0F1419)

val SquareRed = Color(0xFFFF6B6B)
val SquareTurquoise = Color(0xFF4ECDC4)
val SquareBlue = Color(0xFF45B7D1)
val SquareGreen = Color(0xFF96CEB4)
val SquareYellow = Color(0xFFFECA57)
val SquarePurple = Color(0xFF9B59B6)

val UIBackground = Color(0xFF2C3E50)
val UIText = Color(0xFFECF0F1)
val UIAccent = Color(0xFFF39C12)

val ButtonPrimary = Color(0xFF00C851)
val ButtonSecondary = Color(0xFF3498DB)
val ButtonDanger = Color(0xFFE74C3C)

val GlowCyan = Color(0xFF00FFFF)
val GlowRed = Color(0xFFFF0000)
val GlowGold = Color(0xFFFFD700)
