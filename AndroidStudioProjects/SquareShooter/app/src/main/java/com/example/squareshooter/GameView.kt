package com.example.squareshooter

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import kotlin.math.*

@Composable
fun GameScreen(
    gameState: GameState,
    gameEngine: GameEngine,
    onGameOver: () -> Unit
) {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    
    val screenWidth = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeight = with(density) { configuration.screenHeightDp.dp.toPx() }
    
    // Boucle de jeu
    LaunchedEffect(gameState.isGameOver) {
        while (!gameState.isGameOver) {
            gameEngine.updateGame(gameState, null)
            delay(16) // ~60 FPS
        }
        if (gameState.isGameOver) {
            onGameOver()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A2332),
                        Color(0xFF0F1419)
                    )
                )
            )
    ) {
        // Zone de jeu
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .pointerInput(Unit) {
                        detectTapGestures { offset ->
                            gameEngine.handleTouch(gameState, offset)
                        }
                    }
            ) {
                drawGame(gameState, gameEngine, this)
            }
        }
        
        // Interface utilisateur
        GameUI(
            gameState = gameState,
            onAutoToggle = { gameEngine.toggleAutoMode(gameState) },
            onPlaceToggle = { gameEngine.togglePlacementMode(gameState) }
        )
    }
}

@Composable
fun GameUI(
    gameState: GameState,
    onAutoToggle: () -> Unit,
    onPlaceToggle: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(100.dp),
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2C3E50)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Statistiques
            Row(
                horizontalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                StatItem("🎯", "Score", gameState.score.toString())
                StatItem("⚡", "Niveau", gameState.level.toString())
                StatItem("💰", "Argent", gameState.money.toString())
            }
            
            // Contrôles
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Button(
                    onClick = onAutoToggle,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (gameState.isAutoMode) 
                            Color(0xFFE74C3C) else Color(0xFF3498DB)
                    ),
                    shape = RoundedCornerShape(20.dp)
                ) {
                    Text(
                        text = if (gameState.isAutoMode) "AUTO ON" else "AUTO OFF",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                if (gameState.cannons.size < gameState.getAvailableLaunchers()) {
                    Button(
                        onClick = onPlaceToggle,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (gameState.isPlacingLauncher) 
                                Color(0xFFE74C3C) else Color(0xFF3498DB)
                        ),
                        shape = RoundedCornerShape(20.dp)
                    ) {
                        Text(
                            text = "PLACER",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
                
                Text(
                    text = "Lanceurs: ${gameState.cannons.size}",
                    color = Color.White,
                    fontSize = 14.sp
                )
            }
        }
    }
}

@Composable
fun StatItem(icon: String, label: String, value: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = icon,
                fontSize = 16.sp
            )
            Text(
                text = label,
                color = Color(0xFFBDC3C7),
                fontSize = 12.sp,
                fontWeight = FontWeight.SemiBold
            )
        }
        Text(
            text = value,
            color = Color(0xFFF39C12),
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold
        )
    }
}

fun drawGame(gameState: GameState, gameEngine: GameEngine, drawScope: DrawScope) {
    with(drawScope) {
        // Ligne rouge de défaite
        drawLine(
            color = Color.Red,
            start = Offset(0f, gameEngine.getRedLineY()),
            end = Offset(size.width, gameEngine.getRedLineY()),
            strokeWidth = 4.dp.toPx()
        )
        
        // Dessiner les carrés
        gameState.squares.forEach { square ->
            if (square.isActive) {
                drawSquare(square)
            }
        }
        
        // Dessiner les balles
        gameState.bullets.forEach { bullet ->
            if (bullet.isActive) {
                drawBullet(bullet)
            }
        }
        
        // Dessiner les canons
        gameState.cannons.forEach { cannon ->
            drawCannon(cannon)
        }
    }
}

fun DrawScope.drawSquare(square: Square) {
    val bounds = square.getBounds()
    
    // Ombre
    drawRoundRect(
        color = Color.Black.copy(alpha = 0.3f),
        topLeft = bounds.first + Offset(2f, 2f),
        size = androidx.compose.ui.geometry.Size(square.size, square.size),
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(8.dp.toPx())
    )
    
    // Carré principal
    drawRoundRect(
        color = square.color,
        topLeft = bounds.first,
        size = androidx.compose.ui.geometry.Size(square.size, square.size),
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(8.dp.toPx())
    )
    
    // Bordure pour carrés résistants
    if (square.hitsRequired > 1) {
        drawRoundRect(
            color = Color.Red,
            topLeft = bounds.first,
            size = androidx.compose.ui.geometry.Size(square.size, square.size),
            cornerRadius = androidx.compose.ui.geometry.CornerRadius(8.dp.toPx()),
            style = Stroke(width = 3.dp.toPx())
        )
    }
}

fun DrawScope.drawBullet(bullet: Bullet) {
    // Effet de lueur
    drawCircle(
        color = Color(0xFFFFD700).copy(alpha = 0.5f),
        radius = bullet.radius + 4f,
        center = bullet.position
    )
    
    // Balle principale
    drawCircle(
        color = Color(0xFFFFD700),
        radius = bullet.radius,
        center = bullet.position
    )
}

fun DrawScope.drawCannon(cannon: Cannon) {
    val color = if (cannon.isMain) Color(0xFF34495E) else Color(0xFF3498DB)
    
    rotate(
        degrees = Math.toDegrees(cannon.angle.toDouble()).toFloat(),
        pivot = cannon.position
    ) {
        // Corps du canon
        drawRect(
            color = color,
            topLeft = Offset(
                cannon.position.x - cannon.size/4,
                cannon.position.y - cannon.size/2
            ),
            size = androidx.compose.ui.geometry.Size(cannon.size, cannon.size/2)
        )
    }
    
    // Base du canon
    drawCircle(
        color = color,
        radius = cannon.size/3,
        center = cannon.position
    )
}
