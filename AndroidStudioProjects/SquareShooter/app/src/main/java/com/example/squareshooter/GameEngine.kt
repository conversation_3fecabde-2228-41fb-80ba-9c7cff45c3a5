package com.example.squareshooter

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import kotlin.math.*
import kotlin.random.Random

class GameEngine(
    private val screenWidth: Float,
    private val screenHeight: Float
) {
    private val gameHeight = screenHeight - 100f // Zone de jeu (sans UI)
    private val redLineY = gameHeight - 50f // Ligne rouge de défaite
    
    fun initializeGame(gameState: GameState) {
        // Canon principal au centre bas
        val mainCannon = Cannon(
            position = Offset(screenWidth / 2, gameHeight - 20f),
            isMain = true
        )
        gameState.cannons.clear()
        gameState.cannons.add(mainCannon)
        
        // Reset des autres états
        gameState.bullets.clear()
        gameState.squares.clear()
        gameState.score = 0
        gameState.level = 1
        gameState.money = 0
        gameState.isGameOver = false
        gameState.frameCount = 0
        gameState.lastSquareSpawn = 0
        gameState.lastAutoShot = 0
    }
    
    fun updateGame(gameState: GameState, touchPosition: Offset?) {
        if (gameState.isGameOver) return
        
        gameState.frameCount++
        
        // Mise à jour des balles
        updateBullets(gameState)
        
        // Mise à jour des carrés
        updateSquares(gameState)
        
        // Spawn de nouveaux carrés
        spawnSquares(gameState)
        
        // Gestion du tir automatique
        handleAutoShooting(gameState)
        
        // Vérification des collisions
        checkCollisions(gameState)
        
        // Vérification de la défaite
        checkGameOver(gameState)
        
        // Mise à jour du niveau
        gameState.updateLevel()
    }
    
    private fun updateBullets(gameState: GameState) {
        gameState.bullets.removeAll { bullet ->
            bullet.update()
            !bullet.isActive || bullet.isOutOfBounds(screenWidth, gameHeight)
        }
    }
    
    private fun updateSquares(gameState: GameState) {
        gameState.squares.forEach { square ->
            square.update()
        }
    }
    
    private fun spawnSquares(gameState: GameState) {
        if (gameState.shouldSpawnSquare()) {
            val square = Square(
                position = Offset(
                    Random.nextFloat() * (screenWidth - 40f) + 20f,
                    -20f
                ),
                velocity = gameState.getSquareSpeed(),
                hitsRequired = gameState.getSquareHits()
            )
            gameState.squares.add(square)
            gameState.lastSquareSpawn = gameState.frameCount
        }
    }
    
    private fun handleAutoShooting(gameState: GameState) {
        if (gameState.canAutoShoot() && gameState.squares.isNotEmpty()) {
            // Trouver le carré le plus proche
            val closestSquare = gameState.squares
                .filter { it.isActive }
                .minByOrNull { square ->
                    val mainCannon = gameState.cannons.first { it.isMain }
                    sqrt(
                        (square.position.x - mainCannon.position.x).pow(2) +
                        (square.position.y - mainCannon.position.y).pow(2)
                    )
                }
            
            closestSquare?.let { target ->
                // Tous les canons tirent sur la cible
                gameState.cannons.forEach { cannon ->
                    cannon.aimAt(target.position)
                    gameState.bullets.add(cannon.createBullet())
                }
                gameState.lastAutoShot = gameState.frameCount
            }
        }
    }
    
    private fun checkCollisions(gameState: GameState) {
        val bulletsToRemove = mutableListOf<Bullet>()
        val squaresToRemove = mutableListOf<Square>()
        
        gameState.bullets.forEach { bullet ->
            if (!bullet.isActive) return@forEach
            
            gameState.squares.forEach { square ->
                if (!square.isActive) return@forEach
                
                val distance = sqrt(
                    (bullet.position.x - square.position.x).pow(2) +
                    (bullet.position.y - square.position.y).pow(2)
                )
                
                if (distance <= bullet.radius + square.size / 2) {
                    // Collision détectée
                    bullet.isActive = false
                    bulletsToRemove.add(bullet)
                    
                    if (square.hit()) {
                        // Carré détruit
                        square.isActive = false
                        squaresToRemove.add(square)
                        
                        // Calcul des points
                        val isHighZone = square.position.y < gameHeight / 2
                        val basePoints = if (isHighZone) 10 else 15
                        gameState.addScore(basePoints, isHighZone)
                        
                        // Bonus pour carrés résistants
                        if (square.hitsRequired > 1) {
                            gameState.addResistanceBonus(square.hitsRequired)
                        }
                    }
                }
            }
        }
        
        gameState.bullets.removeAll { it in bulletsToRemove }
        gameState.squares.removeAll { it in squaresToRemove }
    }
    
    private fun checkGameOver(gameState: GameState) {
        val hasSquareAtBottom = gameState.squares.any { square ->
            square.isActive && square.isAtBottom(gameHeight)
        }
        
        if (hasSquareAtBottom) {
            gameState.isGameOver = true
        }
    }
    
    fun handleTouch(gameState: GameState, touchPosition: Offset) {
        if (gameState.isGameOver) return
        
        if (gameState.isPlacingLauncher) {
            // Mode placement de lanceur
            placeLauncher(gameState, touchPosition)
        } else {
            // Mode tir normal
            shootAt(gameState, touchPosition)
        }
    }
    
    private fun shootAt(gameState: GameState, target: Offset) {
        val mainCannon = gameState.cannons.first { it.isMain }
        mainCannon.aimAt(target)
        gameState.bullets.add(mainCannon.createBullet())
    }
    
    private fun placeLauncher(gameState: GameState, position: Offset) {
        if (gameState.cannons.size < gameState.getAvailableLaunchers()) {
            val newCannon = Cannon(
                position = position,
                size = 20f,
                isMain = false
            )
            gameState.cannons.add(newCannon)
        }
        gameState.isPlacingLauncher = false
    }
    
    fun toggleAutoMode(gameState: GameState) {
        gameState.isAutoMode = !gameState.isAutoMode
    }
    
    fun togglePlacementMode(gameState: GameState) {
        if (gameState.cannons.size < gameState.getAvailableLaunchers()) {
            gameState.isPlacingLauncher = !gameState.isPlacingLauncher
        }
    }
    
    fun getRedLineY(): Float = redLineY
}
