package com.example.squareshooter

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.squareshooter.ui.theme.SquareShooterTheme
import kotlinx.coroutines.delay
import kotlin.math.*
import kotlin.random.Random

// Classes de données simplifiées
data class SimpleBullet(
    var x: Float,
    var y: Float,
    val velocityX: Float,
    val velocityY: Float,
    var isActive: Boolean = true
)

data class SimpleSquare(
    var x: Float,
    var y: Float,
    val size: Float = 40f,
    val color: Color = Color(0xFFFF6B6B),
    var isActive: Boolean = true
)

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SquareShooterTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    SimpleGameScreen()
                }
            }
        }
    }
}

@Composable
fun SimpleGameScreen() {
    var showMenu by remember { mutableStateOf(true) }
    var score by remember { mutableStateOf(0) }
    var bullets by remember { mutableStateOf(listOf<SimpleBullet>()) }
    var squares by remember { mutableStateOf(listOf<SimpleSquare>()) }
    var cannonX by remember { mutableStateOf(0f) }
    var cannonY by remember { mutableStateOf(0f) }
    var gameRunning by remember { mutableStateOf(false) }

    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    val screenWidth = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeight = with(density) { configuration.screenHeightDp.dp.toPx() }

    // Initialiser la position du canon
    LaunchedEffect(screenWidth, screenHeight) {
        cannonX = screenWidth / 2
        cannonY = screenHeight - 150f
    }

    // Boucle de jeu
    LaunchedEffect(gameRunning) {
        while (gameRunning) {
            // Mettre à jour les balles
            bullets = bullets.map { bullet ->
                bullet.copy(
                    x = bullet.x + bullet.velocityX,
                    y = bullet.y + bullet.velocityY,
                    isActive = bullet.isActive && bullet.y > 0 && bullet.x > 0 && bullet.x < screenWidth
                )
            }.filter { it.isActive }

            // Mettre à jour les carrés
            squares = squares.map { square ->
                square.copy(y = square.y + 2f)
            }.filter { it.y < screenHeight }

            // Ajouter de nouveaux carrés
            if (Random.nextFloat() < 0.02f) {
                val newSquare = SimpleSquare(
                    x = Random.nextFloat() * (screenWidth - 40f),
                    y = -40f,
                    color = listOf(
                        Color(0xFFFF6B6B),
                        Color(0xFF4ECDC4),
                        Color(0xFF45B7D1),
                        Color(0xFF96CEB4),
                        Color(0xFFFECA57)
                    ).random()
                )
                squares = squares + newSquare
            }

            // Vérifier les collisions
            val newBullets = mutableListOf<SimpleBullet>()
            val newSquares = mutableListOf<SimpleSquare>()

            bullets.forEach { bullet ->
                var bulletHit = false
                squares.forEach { square ->
                    val distance = sqrt(
                        (bullet.x - square.x).pow(2) + (bullet.y - square.y).pow(2)
                    )
                    if (distance < 30f && !bulletHit) {
                        bulletHit = true
                        score += 10
                    } else if (!bulletHit) {
                        newSquares.add(square)
                    }
                }
                if (!bulletHit) {
                    newBullets.add(bullet)
                }
            }

            bullets = newBullets
            squares = newSquares.distinctBy { "${it.x}-${it.y}" }

            delay(16) // ~60 FPS
        }
    }

    if (showMenu) {
        MainMenu(
            onStartGame = {
                showMenu = false
                gameRunning = true
                score = 0
                bullets = emptyList()
                squares = emptyList()
            }
        )
    } else {
        GameContent(
            score = score,
            bullets = bullets,
            squares = squares,
            cannonX = cannonX,
            cannonY = cannonY,
            onTouch = { offset ->
                val angle = atan2(offset.y - cannonY, offset.x - cannonX)
                val speed = 15f
                val newBullet = SimpleBullet(
                    x = cannonX,
                    y = cannonY,
                    velocityX = cos(angle) * speed,
                    velocityY = sin(angle) * speed
                )
                bullets = bullets + newBullet
            },
            onBackToMenu = {
                showMenu = true
                gameRunning = false
            }
        )
    }
}

@Composable
fun GameContent(
    score: Int,
    bullets: List<SimpleBullet>,
    squares: List<SimpleSquare>,
    cannonX: Float,
    cannonY: Float,
    onTouch: (Offset) -> Unit,
    onBackToMenu: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A2332),
                        Color(0xFF0F1419)
                    )
                )
            )
    ) {
        // Zone de jeu
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .pointerInput(Unit) {
                        detectTapGestures { offset ->
                            onTouch(offset)
                        }
                    }
            ) {
                // Fond avec grille
                drawRect(
                    color = Color.Black.copy(alpha = 0.2f),
                    topLeft = Offset(0f, 0f),
                    size = size
                )

                // Ligne rouge de défaite
                drawLine(
                    color = Color.Red,
                    start = Offset(0f, size.height - 100f),
                    end = Offset(size.width, size.height - 100f),
                    strokeWidth = 4.dp.toPx()
                )

                // Dessiner les carrés
                squares.forEach { square ->
                    drawRect(
                        color = square.color,
                        topLeft = Offset(square.x - square.size/2, square.y - square.size/2),
                        size = androidx.compose.ui.geometry.Size(square.size, square.size)
                    )
                }

                // Dessiner les balles
                bullets.forEach { bullet ->
                    drawCircle(
                        color = Color.Yellow,
                        radius = 8f,
                        center = Offset(bullet.x, bullet.y)
                    )
                }

                // Dessiner le canon
                drawCircle(
                    color = Color.Blue,
                    radius = 20f,
                    center = Offset(cannonX, cannonY)
                )
            }
        }

        // Interface utilisateur
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp),
            shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF2C3E50)
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Score: $score",
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )

                Button(
                    onClick = onBackToMenu,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFE74C3C)
                    )
                ) {
                    Text("MENU")
                }
            }
        }
    }
}

@Composable
fun MainMenu(onStartGame: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A2332),
                        Color(0xFF0F1419)
                    )
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.3f)
            )
        ) {
            Column(
                modifier = Modifier
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                Text(
                    text = "🎯 SQUARE SHOOTER",
                    fontSize = 36.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF00FFFF),
                    textAlign = TextAlign.Center
                )

                Text(
                    text = "Détruisez les carrés qui tombent !",
                    fontSize = 18.sp,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )

                Text(
                    text = "Touchez l'écran pour tirer",
                    fontSize = 16.sp,
                    color = Color(0xFFFFD700),
                    textAlign = TextAlign.Center
                )

                Button(
                    onClick = onStartGame,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF00C851)
                    )
                ) {
                    Text(
                        text = "COMMENCER",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}
