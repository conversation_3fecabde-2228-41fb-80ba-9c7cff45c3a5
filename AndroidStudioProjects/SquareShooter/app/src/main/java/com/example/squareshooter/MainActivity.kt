package com.example.squareshooter

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.squareshooter.ui.theme.SquareShooterTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SquareShooterTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    GameApp()
                }
            }
        }
    }
}

@Composable
fun GameApp() {
    var gameState by remember { mutableStateOf<GameState?>(null) }
    var gameEngine by remember { mutableStateOf<GameEngine?>(null) }
    var showMenu by remember { mutableStateOf(true) }

    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    val screenWidth = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeight = with(density) { configuration.screenHeightDp.dp.toPx() }

    if (showMenu) {
        MainMenu(
            onStartGame = {
                val newGameState = GameState()
                val newGameEngine = GameEngine(screenWidth, screenHeight)
                newGameEngine.initializeGame(newGameState)
                gameState = newGameState
                gameEngine = newGameEngine
                showMenu = false
            }
        )
    } else {
        gameState?.let { state ->
            gameEngine?.let { engine ->
                GameScreen(
                    gameState = state,
                    gameEngine = engine,
                    onGameOver = {
                        showMenu = true
                        gameState = null
                        gameEngine = null
                    }
                )
            }
        }
    }
}

@Composable
fun MainMenu(onStartGame: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A2332),
                        Color(0xFF0F1419)
                    )
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.3f)
            )
        ) {
            Column(
                modifier = Modifier
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                Text(
                    text = "🎯 SQUARE SHOOTER",
                    fontSize = 36.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF00FFFF),
                    textAlign = TextAlign.Center
                )

                Text(
                    text = "Détruisez les carrés qui tombent !",
                    fontSize = 18.sp,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )

                Text(
                    text = "Touchez l'écran pour tirer",
                    fontSize = 16.sp,
                    color = Color(0xFFFFD700),
                    textAlign = TextAlign.Center
                )

                Button(
                    onClick = onStartGame,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF00C851)
                    )
                ) {
                    Text(
                        text = "COMMENCER",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}
