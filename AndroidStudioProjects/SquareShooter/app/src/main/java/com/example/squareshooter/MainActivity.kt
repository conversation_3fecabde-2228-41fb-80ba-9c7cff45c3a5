package com.example.squareshooter

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.squareshooter.ui.theme.SquareShooterTheme
import kotlinx.coroutines.delay
import kotlin.math.*
import kotlin.random.Random



class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SquareShooterTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    SquareShooterGame()
                }
            }
        }
    }
}

@Composable
fun SquareShooterGame() {
    val context = LocalContext.current
    val prefs = remember { context.getSharedPreferences("SquareShooter", Context.MODE_PRIVATE) }

    var gameState by remember { mutableStateOf<GameState?>(null) }
    var gameScreen by remember { mutableStateOf("menu") } // "menu", "game", "gameover"
    var bestScore by remember { mutableStateOf(prefs.getInt("bestScore", 0)) }

    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    val screenWidth = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeight = with(density) { configuration.screenHeightDp.dp.toPx() }
    val gameHeight = screenHeight - 100f // Zone de jeu (sans UI)

    // Fonction pour sauvegarder le meilleur score
    fun saveBestScore(score: Int) {
        prefs.edit().putInt("bestScore", score).apply()
        bestScore = score
    }

    // Boucle de jeu principale selon spécifications
    LaunchedEffect(gameState) {
        gameState?.let { state ->
            while (!state.isGameOver && gameScreen == "game") {
                state.frameCount++

                // Mise à jour des balles
                state.bullets.removeAll { bullet ->
                    bullet.update()
                    !bullet.isActive || bullet.isOutOfBounds(screenWidth, gameHeight)
                }

                // Mise à jour des carrés
                state.squares.forEach { square ->
                    square.update()
                }

                // Spawn de nouveaux carrés selon spécifications
                if (state.shouldSpawnSquare()) {
                    val square = Square(
                        position = Offset(
                            Random.nextFloat() * (screenWidth - 40f) + 20f,
                            -20f
                        ),
                        velocity = state.getSquareSpeed(),
                        hitsRequired = state.getSquareHits()
                    )
                    state.squares.add(square)
                    state.lastSquareSpawn = state.frameCount
                }

                // Gestion du tir automatique (Mode Auto-Tir)
                if (state.canAutoShoot() && state.squares.isNotEmpty()) {
                    // Ciblage intelligent : Vise automatiquement le carré le plus proche
                    val closestSquare = state.squares
                        .filter { it.isActive }
                        .minByOrNull { square ->
                            val mainCannon = state.cannons.first { it.isMain }
                            sqrt(
                                (square.position.x - mainCannon.position.x).pow(2) +
                                (square.position.y - mainCannon.position.y).pow(2)
                            )
                        }

                    closestSquare?.let { target ->
                        // Multi-lanceurs : Tous les canons tirent simultanément
                        state.cannons.forEach { cannon ->
                            cannon.aimAt(target.position)
                            state.bullets.add(cannon.createBullet())
                        }
                        state.lastAutoShot = state.frameCount
                    }
                }

                // Vérification des collisions (Détection de collision précise)
                val bulletsToRemove = mutableListOf<Bullet>()
                val squaresToRemove = mutableListOf<Square>()

                state.bullets.forEach { bullet ->
                    if (!bullet.isActive) return@forEach

                    state.squares.forEach { square ->
                        if (!square.isActive) return@forEach

                        // Détection de collision précise (rayon de balle + centre du carré)
                        val distance = sqrt(
                            (bullet.position.x - square.position.x).pow(2) +
                            (bullet.position.y - square.position.y).pow(2)
                        )

                        if (distance <= bullet.radius + square.size / 2) {
                            bullet.isActive = false
                            bulletsToRemove.add(bullet)

                            if (square.hit()) {
                                square.isActive = false
                                squaresToRemove.add(square)

                                // Système de scoring selon spécifications
                                val isHighZone = square.position.y < gameHeight / 2
                                val basePoints = if (isHighZone) 10 else 15
                                state.addScore(basePoints, isHighZone)

                                // Bonus pour carrés résistants
                                if (square.hitsRequired > 1) {
                                    state.addResistanceBonus(square.hitsRequired)
                                }
                            }
                        }
                    }
                }

                state.bullets.removeAll { it in bulletsToRemove }
                state.squares.removeAll { it in squaresToRemove }

                // Vérification de la défaite
                val hasSquareAtBottom = state.squares.any { square ->
                    square.isActive && square.isAtBottom(gameHeight)
                }

                if (hasSquareAtBottom) {
                    state.isGameOver = true
                    // Vérifier nouveau record
                    if (state.checkNewRecord(bestScore)) {
                        saveBestScore(state.score)
                    }
                    gameScreen = "gameover"
                }

                // Mise à jour du niveau
                state.updateLevel()

                delay(16) // ~60 FPS
            }
        }
    }

    when (gameScreen) {
        "menu" -> {
            StartScreen(
                bestScore = bestScore,
                onStartGame = {
                    val newGameState = GameState()
                    // Canon principal au bas de l'écran avec visée libre 360°
                    val mainCannon = Cannon(
                        position = Offset(screenWidth / 2, gameHeight - 20f),
                        isMain = true
                    )
                    newGameState.cannons.add(mainCannon)
                    newGameState.bestScore = bestScore

                    gameState = newGameState
                    gameScreen = "game"
                }
            )
        }

        "game" -> {
            gameState?.let { state ->
                GameScreen(
                    gameState = state,
                    screenWidth = screenWidth,
                    screenHeight = screenHeight,
                    gameHeight = gameHeight,
                    onTouch = { offset ->
                        if (!state.isGameOver) {
                            if (state.isPlacingLauncher) {
                                // Mode placement de lanceur
                                if (state.cannons.size < state.getAvailableLaunchers()) {
                                    val newCannon = Cannon(
                                        position = offset,
                                        size = 20f,
                                        isMain = false
                                    )
                                    state.cannons.add(newCannon)
                                }
                                state.isPlacingLauncher = false
                            } else {
                                // Mode tir normal - Visée libre 360°
                                val mainCannon = state.cannons.first { it.isMain }
                                mainCannon.aimAt(offset)
                                state.bullets.add(mainCannon.createBullet())
                            }
                        }
                    },
                    onAutoToggle = {
                        state.isAutoMode = !state.isAutoMode
                    },
                    onPlaceToggle = {
                        if (state.cannons.size < state.getAvailableLaunchers()) {
                            state.isPlacingLauncher = !state.isPlacingLauncher
                        }
                    }
                )
            }
        }

        "gameover" -> {
            gameState?.let { state ->
                GameOverScreen(
                    finalScore = state.score,
                    bestScore = bestScore,
                    level = state.level,
                    isNewRecord = state.isNewRecord,
                    onReplay = {
                        gameScreen = "menu"
                        gameState = null
                    }
                )
            }
        }
    }
}

// Écran de Démarrage selon spécifications
@Composable
fun StartScreen(
    bestScore: Int,
    onStartGame: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                // Arrière-plan : Dégradé radial violet-bleu
                brush = Brush.radialGradient(
                    colors = listOf(
                        Color(0xFF9B59B6),
                        Color(0xFF3498DB),
                        Color(0xFF1A2332)
                    ),
                    radius = 1000f
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight(),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.4f)
            ),
            shape = RoundedCornerShape(20.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                // Titre : "🎯 SQUARE SHOOTER" avec effet néon cyan
                Text(
                    text = "🎯 SQUARE SHOOTER",
                    fontSize = 36.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF00FFFF),
                    textAlign = TextAlign.Center,
                    style = androidx.compose.ui.text.TextStyle(
                        shadow = androidx.compose.ui.graphics.Shadow(
                            color = Color(0xFF00FFFF),
                            blurRadius = 10f
                        )
                    )
                )

                // Instructions : Guide rapide du gameplay
                Text(
                    text = "Détruisez les carrés qui tombent !",
                    fontSize = 18.sp,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )

                Text(
                    text = "• Touchez l'écran pour tirer\n• Mode AUTO disponible\n• Placez des lanceurs supplémentaires",
                    fontSize = 14.sp,
                    color = Color(0xFFBDC3C7),
                    textAlign = TextAlign.Center,
                    lineHeight = 20.sp
                )

                if (bestScore > 0) {
                    Text(
                        text = "Meilleur Score: $bestScore",
                        fontSize = 16.sp,
                        color = Color(0xFFFFD700),
                        fontWeight = FontWeight.Bold
                    )
                }

                // Bouton : "COMMENCER" vert moderne
                Button(
                    onClick = onStartGame,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF00C851)
                    ),
                    shape = RoundedCornerShape(25.dp),
                    modifier = Modifier
                        .fillMaxWidth(0.7f)
                        .height(50.dp)
                ) {
                    Text(
                        text = "COMMENCER",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
            }
        }
    }
}

@Composable
fun GameScreen(
    gameState: GameState,
    screenWidth: Float,
    screenHeight: Float,
    gameHeight: Float,
    onTouch: (Offset) -> Unit,
    onAutoToggle: () -> Unit,
    onPlaceToggle: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                // Arrière-plan : Dégradé sombre (bleu nuit → bleu marine)
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A2332),
                        Color(0xFF0F1419)
                    )
                )
            )
    ) {
        // Zone de Jeu (800×500 pixels équivalent)
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            Canvas(
                modifier = Modifier
                    .fillMaxSize()
                    .pointerInput(Unit) {
                        detectTapGestures { offset ->
                            onTouch(offset)
                        }
                    }
            ) {
                // Arrière-plan de la zone de jeu
                drawRect(
                    color = Color.Black.copy(alpha = 0.1f),
                    topLeft = Offset(0f, 0f),
                    size = size
                )

                // Séparation : Ligne rouge dégradée entre jeu et interface
                val redLineY = gameHeight - 50f
                drawLine(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color.Transparent,
                            Color.Red,
                            Color(0xFFFF6B6B),
                            Color.Red,
                            Color.Transparent
                        )
                    ),
                    start = Offset(0f, redLineY),
                    end = Offset(size.width, redLineY),
                    strokeWidth = 4.dp.toPx()
                )

                // Dessiner les carrés avec design moderne
                gameState.squares.forEach { square ->
                    if (square.isActive) {
                        drawSquareModern(square)
                    }
                }

                // Dessiner les balles dorées avec effet de lueur
                gameState.bullets.forEach { bullet ->
                    if (bullet.isActive) {
                        drawBulletGolden(bullet)
                    }
                }

                // Dessiner les canons
                gameState.cannons.forEach { cannon ->
                    drawCannonModern(cannon)
                }
            }
        }

        // Interface Moderne (100 pixels pour l'UI)
        ModernGameUI(
            gameState = gameState,
            onAutoToggle = onAutoToggle,
            onPlaceToggle = onPlaceToggle
        )
    }
}


