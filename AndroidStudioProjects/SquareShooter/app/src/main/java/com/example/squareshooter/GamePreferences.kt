package com.example.squareshooter

import android.content.Context
import android.content.SharedPreferences

class GamePreferences(context: Context) {
    private val prefs: SharedPreferences = context.getSharedPreferences(
        "square_shooter_prefs", 
        Context.MODE_PRIVATE
    )
    
    companion object {
        private const val KEY_BEST_SCORE = "best_score"
        private const val KEY_TOTAL_GAMES = "total_games"
        private const val KEY_TOTAL_SQUARES_DESTROYED = "total_squares_destroyed"
        private const val KEY_HIGHEST_LEVEL = "highest_level"
    }
    
    fun getBestScore(): Int {
        return prefs.getInt(KEY_BEST_SCORE, 0)
    }
    
    fun setBestScore(score: Int) {
        prefs.edit().putInt(KEY_BEST_SCORE, score).apply()
    }
    
    fun isNewRecord(score: Int): Boolean {
        val currentBest = getBestScore()
        if (score > currentBest) {
            setBestScore(score)
            return true
        }
        return false
    }
    
    fun getTotalGames(): Int {
        return prefs.getInt(KEY_TOTAL_GAMES, 0)
    }
    
    fun incrementTotalGames() {
        val current = getTotalGames()
        prefs.edit().putInt(KEY_TOTAL_GAMES, current + 1).apply()
    }
    
    fun getTotalSquaresDestroyed(): Int {
        return prefs.getInt(KEY_TOTAL_SQUARES_DESTROYED, 0)
    }
    
    fun addSquaresDestroyed(count: Int) {
        val current = getTotalSquaresDestroyed()
        prefs.edit().putInt(KEY_TOTAL_SQUARES_DESTROYED, current + count).apply()
    }
    
    fun getHighestLevel(): Int {
        return prefs.getInt(KEY_HIGHEST_LEVEL, 1)
    }
    
    fun updateHighestLevel(level: Int) {
        val current = getHighestLevel()
        if (level > current) {
            prefs.edit().putInt(KEY_HIGHEST_LEVEL, level).apply()
        }
    }
    
    fun resetAllStats() {
        prefs.edit().clear().apply()
    }
}
