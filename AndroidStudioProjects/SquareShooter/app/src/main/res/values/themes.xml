<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.SquareShooter" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/button_primary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/button_secondary</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:navigationBarColor">@color/black</item>
        <item name="android:windowBackground">@color/game_background_start</item>
    </style>

    <style name="Theme.SquareShooter" parent="Base.Theme.SquareShooter" />
</resources>
